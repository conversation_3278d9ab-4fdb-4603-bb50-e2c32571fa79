{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiSettings": {"BaseUrl": "http://localhost:5000", "CatalogApiUrl": "http://localhost:5000/api/catalog", "IdentityApiUrl": "http://localhost:5155", "OrderApiUrl": "http://localhost:5000/api/orders", "CartApiUrl": "http://localhost:5000/api/cart", "PaymentApiUrl": "http://localhost:5000/api/payments", "ReviewApiUrl": "http://localhost:5000/api/reviews"}}