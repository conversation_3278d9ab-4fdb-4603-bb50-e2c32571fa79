﻿using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.AdminPortal.Models.Orders;
using NafaPlace.AdminPortal.Models.Common;
using NafaPlace.AdminPortal.Models.Users;
using AuthUserDto = NafaPlace.AdminPortal.Models.Auth.UserDto;
using Blazored.LocalStorage;
using System.Net.Http.Headers;

namespace NafaPlace.AdminPortal.Services;

public interface IOrderService
{
    Task<PagedResult<OrderDto>> GetOrdersAsync(OrderSearchRequest request);
    Task<OrderDto?> GetOrderByIdAsync(string orderNumber);
    Task<OrderDto> UpdateOrderStatusAsync(string orderNumber, UpdateOrderStatusRequest request);
    Task<OrderStatistics> GetOrderStatisticsAsync();
}

public class OrderService : IOrderService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly IAuthService _authService;
    private readonly ILocalStorageService _localStorage;
    private readonly IConfiguration _configuration;

    public OrderService(HttpClient httpClient, IAuthService authService, ILocalStorageService localStorage, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _authService = authService;
        _localStorage = localStorage;
        _configuration = configuration;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<PagedResult<OrderDto>> GetOrdersAsync(OrderSearchRequest request)
    {
        try
        {
            var queryParams = new List<string>
            {
                $"pageNumber={request.PageNumber}",
                $"pageSize={request.PageSize}"
            };

            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");

            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={Uri.EscapeDataString(request.Status)}");

            if (!string.IsNullOrEmpty(request.PaymentStatus))
                queryParams.Add($"paymentStatus={Uri.EscapeDataString(request.PaymentStatus)}");

            if (request.StartDate.HasValue)
                queryParams.Add($"startDate={request.StartDate.Value:yyyy-MM-dd}");

            if (request.EndDate.HasValue)
                queryParams.Add($"endDate={request.EndDate.Value:yyyy-MM-dd}");

            var query = string.Join("&", queryParams);
            var orderApiUrl = _configuration["ApiSettings:OrderApiUrl"];
            var response = await _httpClient.GetAsync($"{orderApiUrl}?{query}");
            response.EnsureSuccessStatusCode();

            // Lire les donnÃ©es de l'API Order
            var apiOrders = await response.Content.ReadFromJsonAsync<List<OrderApiDto>>(_jsonOptions);

            if (apiOrders != null)
            {
                // Convertir les donnÃ©es de l'API vers nos DTOs avec enrichissement des donnÃ©es utilisateur
                var orderDtos = new List<OrderDto>();

                foreach (var o in apiOrders)
                {
                    var userInfo = await GetUserInfoAsync(o.UserId);

                    orderDtos.Add(new OrderDto
                    {
                        Id = 0, // Pas d'ID int dans l'API, on utilise 0 pour le moment
                        OrderNumber = o.Id.ToString(), // Utiliser l'ID comme numÃ©ro de commande
                        UserId = userInfo.Id,
                        UserName = userInfo.FullName,
                        UserEmail = userInfo.Email,
                    OrderDate = o.OrderDate,
                    TotalAmount = o.TotalAmount,
                    Currency = o.Currency,
                    Status = MapOrderStatus(o.Status),
                    PaymentStatus = MapPaymentStatus(o.PaymentStatus),
                    PaymentMethod = MapPaymentMethod(o.PaymentMethod),
                    ShippingAddress = o.ShippingAddress != null ? new ShippingAddressDto
                    {
                        FirstName = o.ShippingAddress.FullName.Split(' ').FirstOrDefault() ?? "",
                        LastName = o.ShippingAddress.FullName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                        AddressLine1 = o.ShippingAddress.Address,
                        City = o.ShippingAddress.City,
                        Country = o.ShippingAddress.Country,
                        PostalCode = o.ShippingAddress.PostalCode,
                        PhoneNumber = o.ShippingAddress.PhoneNumber ?? ""
                    } : null,
                        Items = o.OrderItems?.Select(item => new OrderItemDto
                        {
                            Id = 0, // Pas d'ID int dans l'API
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            UnitPrice = item.UnitPrice,
                            Quantity = item.Quantity,
                            TotalPrice = item.UnitPrice * item.Quantity,
                            ProductImage = item.ImageUrl ?? string.Empty
                        }).ToList() ?? new List<OrderItemDto>()
                    });
                }

                // Appliquer la pagination
                var totalCount = orderDtos.Count;
                var pagedItems = orderDtos
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                return new PagedResult<OrderDto>
                {
                    Items = pagedItems,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }

            return new PagedResult<OrderDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des commandes: {ex.Message}");
            return new PagedResult<OrderDto>();
        }
    }

    public async Task<OrderDto?> GetOrderByIdAsync(string orderNumber)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            // Convertir le numéro de commande en int
            if (int.TryParse(orderNumber, out var orderId))
            {
                var orderApiUrl = _configuration["ApiSettings:OrderApiUrl"];
                var response = await _httpClient.GetAsync($"{orderApiUrl}/{orderId}");
                if (response.IsSuccessStatusCode)
                {
                    var apiOrder = await response.Content.ReadFromJsonAsync<OrderApiDto>(_jsonOptions);
                    if (apiOrder != null)
                    {
                        var userInfo = await GetUserInfoAsync(apiOrder.UserId);

                        return new OrderDto
                        {
                            Id = 0,
                            OrderNumber = apiOrder.Id.ToString(),
                            UserId = userInfo.Id,
                            UserName = userInfo.FullName,
                            UserEmail = userInfo.Email,
                            OrderDate = apiOrder.OrderDate,
                            TotalAmount = apiOrder.TotalAmount,
                            Currency = apiOrder.Currency,
                            Status = MapOrderStatus(apiOrder.Status),
                            PaymentStatus = MapPaymentStatus(apiOrder.PaymentStatus),
                            PaymentMethod = MapPaymentMethod(apiOrder.PaymentMethod),
                            ShippingAddress = apiOrder.ShippingAddress != null ? new ShippingAddressDto
                            {
                                FirstName = apiOrder.ShippingAddress.FullName.Split(' ').FirstOrDefault() ?? "",
                                LastName = apiOrder.ShippingAddress.FullName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                                AddressLine1 = apiOrder.ShippingAddress.Address,
                                City = apiOrder.ShippingAddress.City,
                                Country = apiOrder.ShippingAddress.Country,
                                PostalCode = apiOrder.ShippingAddress.PostalCode,
                                PhoneNumber = apiOrder.ShippingAddress.PhoneNumber ?? ""
                            } : null,
                            Items = apiOrder.OrderItems?.Select(item => new OrderItemDto
                            {
                                Id = 0,
                                ProductId = item.ProductId,
                                ProductName = item.ProductName,
                                UnitPrice = item.UnitPrice,
                                Quantity = item.Quantity,
                                TotalPrice = item.UnitPrice * item.Quantity,
                                ProductImage = item.ImageUrl ?? string.Empty
                            }).ToList() ?? new List<OrderItemDto>()
                        };
                    }
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration de la commande {orderNumber}: {ex.Message}");
            return null;
        }
    }

    public async Task<OrderDto> UpdateOrderStatusAsync(string orderNumber, UpdateOrderStatusRequest request)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            // Convertir le numéro de commande en int
            if (int.TryParse(orderNumber, out var orderId))
            {
                // Convertir le statut français vers l'enum API
                var apiRequest = new UpdateOrderStatusRequest
                {
                    Status = MapOrderStatusToApi(request.Status),
                    TrackingNumber = request.TrackingNumber,
                    Notes = request.Notes
                };

                var orderApiUrl = _configuration["ApiSettings:OrderApiUrl"];
                var response = await _httpClient.PutAsJsonAsync($"{orderApiUrl}/{orderId}/status", apiRequest, _jsonOptions);
                response.EnsureSuccessStatusCode();

                // L'API retourne UpdateOrderStatusResponse, pas OrderDto
                // Récupérer la commande mise à jour
                var orderResponse = await _httpClient.GetAsync($"{orderApiUrl}/{orderId}");
                orderResponse.EnsureSuccessStatusCode();

                var apiOrder = await orderResponse.Content.ReadFromJsonAsync<OrderApiDto>(_jsonOptions);
                if (apiOrder != null)
                {
                    var userInfo = await GetUserInfoAsync(apiOrder.UserId);

                    return new OrderDto
                    {
                        Id = 0,
                        OrderNumber = apiOrder.Id.ToString(),
                        UserId = userInfo.Id,
                        UserName = userInfo.FullName,
                        UserEmail = userInfo.Email,
                        OrderDate = apiOrder.OrderDate,
                        TotalAmount = apiOrder.TotalAmount,
                        Currency = apiOrder.Currency,
                        Status = MapOrderStatus(apiOrder.Status),
                        PaymentStatus = MapPaymentStatus(apiOrder.PaymentStatus),
                        PaymentMethod = MapPaymentMethod(apiOrder.PaymentMethod),
                        ShippingAddress = apiOrder.ShippingAddress != null ? new ShippingAddressDto
                        {
                            FirstName = apiOrder.ShippingAddress.FullName.Split(' ').FirstOrDefault() ?? "",
                            LastName = apiOrder.ShippingAddress.FullName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                            AddressLine1 = apiOrder.ShippingAddress.Address,
                            City = apiOrder.ShippingAddress.City,
                            Country = apiOrder.ShippingAddress.Country,
                            PostalCode = apiOrder.ShippingAddress.PostalCode,
                            PhoneNumber = apiOrder.ShippingAddress.PhoneNumber
                        } : null,
                        Items = apiOrder.OrderItems?.Select(item => new OrderItemDto
                        {
                            Id = 0,
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            UnitPrice = item.UnitPrice,
                            Quantity = item.Quantity,
                            TotalPrice = item.UnitPrice * item.Quantity,
                            ProductImage = item.ImageUrl ?? string.Empty
                        }).ToList() ?? new List<OrderItemDto>()
                    };
                }
                throw new Exception("Réponse invalide du serveur");
            }
            throw new Exception("Numéro de commande invalide");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise Ã  jour du statut de la commande {orderNumber}: {ex.Message}");
            throw;
        }
    }

    public async Task<OrderStatistics> GetOrderStatisticsAsync()
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            // Récupérer toutes les commandes pour calculer les statistiques
            var orderApiUrl = _configuration["ApiSettings:OrderApiUrl"];
            var response = await _httpClient.GetAsync(orderApiUrl);

            if (response.IsSuccessStatusCode)
            {
                var apiOrders = await response.Content.ReadFromJsonAsync<List<OrderApiDto>>(_jsonOptions);

                if (apiOrders != null)
                {
                    var totalOrders = apiOrders.Count;
                    var pendingOrders = apiOrders.Count(o => o.Status == "Pending");
                    var processingOrders = apiOrders.Count(o => o.Status == "Processing");
                    var shippedOrders = apiOrders.Count(o => o.Status == "Shipped");
                    var deliveredOrders = apiOrders.Count(o => o.Status == "Delivered");
                    var cancelledOrders = apiOrders.Count(o => o.Status == "Cancelled");
                    var totalRevenue = apiOrders.Where(o => o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid").Sum(o => o.TotalAmount);
                    var monthlyRevenue = apiOrders.Where(o => (o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid") && o.OrderDate >= DateTime.Now.AddMonths(-1)).Sum(o => o.TotalAmount);
                    var averageOrderValue = totalOrders > 0 ? apiOrders.Average(o => o.TotalAmount) : 0;

                    return new OrderStatistics
                    {
                        TotalOrders = totalOrders,
                        PendingOrders = pendingOrders,
                        ProcessingOrders = processingOrders,
                        ShippedOrders = shippedOrders,
                        DeliveredOrders = deliveredOrders,
                        CancelledOrders = cancelledOrders,
                        TotalRevenue = totalRevenue,
                        MonthlyRevenue = monthlyRevenue,
                        AverageOrderValue = averageOrderValue
                    };
                }
            }

            return new OrderStatistics();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des statistiques: {ex.Message}");
            return new OrderStatistics();
        }
    }

    private async Task<UserDto> GetUserInfoAsync(string userId)
    {
        try
        {
            // Essayer de convertir l'userId string en int pour l'API Identity
            if (int.TryParse(userId, out var userIdInt))
            {
                // RÃ©cupÃ©rer le token d'authentification
                var token = await _localStorage.GetItemAsync<string>("authToken");
                if (!string.IsNullOrEmpty(token))
                {
                    // Configurer l'en-tÃªte d'autorisation pour l'appel direct Ã  l'API Identity
                    // Utiliser l'AuthService pour rÃ©cupÃ©rer les informations utilisateur
                    var authUser = await _authService.GetUserByIdAsync(userIdInt);
                    if (authUser != null && !string.IsNullOrEmpty(authUser.Email))
                    {
                        // Convertir AuthUserDto vers UserDto (Models.Users)
                        return new UserDto
                        {
                            Id = authUser.Id,
                            Email = authUser.Email,
                            FirstName = authUser.FirstName,
                            LastName = authUser.LastName,
                            PhoneNumber = authUser.PhoneNumber,
                            IsActive = authUser.IsActive,
                            IsEmailConfirmed = authUser.EmailConfirmed,
                            Roles = authUser.Roles,
                            CreatedAt = DateTime.UtcNow, // Valeur par dÃ©faut
                            LastLoginAt = null
                        };
                    }
                    else
                    {
                        Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des informations utilisateur pour userId {userIdInt}");
                    }
                }
                else
                {
                    Console.WriteLine("Token d'authentification non trouvÃ©");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des informations utilisateur pour {userId}: {ex.Message}");
        }

        // Retourner des informations par dÃ©faut si la rÃ©cupÃ©ration Ã©choue
        return new UserDto
        {
            Id = int.TryParse(userId, out var id) ? id : 0,
            Email = $"user{userId}@nafaplace.com",
            FirstName = "Utilisateur",
            LastName = userId,
            PhoneNumber = "",
            IsActive = true,
            IsEmailConfirmed = false,
            Roles = new List<string>()
        };
    }

    private string MapOrderStatus(string apiStatus)
    {
        return apiStatus switch
        {
            "Pending" => "En attente",
            "Paid" => "Payé",
            "Shipped" => "Expédié",
            "Delivered" => "Livré",
            "Cancelled" => "Annulé",
            _ => apiStatus
        };
    }

    private string MapPaymentStatus(string apiPaymentStatus)
    {
        return apiPaymentStatus switch
        {
            "Pending" => "En attente",
            "Processing" => "En cours",
            "Completed" => "Payé",
            "Failed" => "Échoué",
            "Cancelled" => "Annulé",
            "Refunded" => "Remboursé",
            _ => apiPaymentStatus
        };
    }

    private string MapPaymentMethod(string apiPaymentMethod)
    {
        return apiPaymentMethod switch
        {
            "CashOnDelivery" => "Paiement à la livraison",
            "OrangeMoney" => "Orange Money",
            "Stripe" => "Carte bancaire",
            _ => apiPaymentMethod
        };
    }

    // Méthode inverse pour convertir les statuts français vers les enums API
    private string MapOrderStatusToApi(string displayStatus)
    {
        return displayStatus switch
        {
            "En attente" => "Pending",
            "En cours de traitement" => "Processing",
            "Expédié" => "Shipped",
            "Livré" => "Delivered",
            "Annulé" => "Cancelled",
            "Remboursé" => "Refunded",
            _ => displayStatus
        };
    }
}

