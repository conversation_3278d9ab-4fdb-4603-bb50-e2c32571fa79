@page "/products"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using System.ComponentModel.DataAnnotations
@using Microsoft.JSInterop
@using NafaPlace.SellerPortal.Services
@using NafaPlace.Catalog.Domain.Models
@using InputFile = Microsoft.AspNetCore.Components.Forms.InputFile
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject ProductService ProductService
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService
@inject ImageService ImageService
@inject IAuthService AuthService
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IAsyncDisposable

<h1 class="visually-hidden">Gestion des Produits - NafaPlace</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Produits</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Produits</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-grid me-1"></i>
                Liste des Produits
            </div>
            <button class="btn btn-primary" @onclick="async () => await OpenAddProductModal()">
                <i class="bi bi-plus-circle me-1"></i> Ajouter un Produit
            </button>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Rechercher un produit..." @bind="_searchTerm" @bind:event="oninput">
                        <button class="btn btn-outline-secondary" type="button" @onclick="OnSearch">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" @bind="_selectedCategoryId" @bind:event="oninput" @onchange="OnCategoryFilterChange">
                        <option value="">Toutes les catégories</option>
                        @foreach (var category in _categories)
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" @bind="_activeFilter" @bind:event="oninput" @onchange="OnActiveFilterChange">
                        <option value="">Tous les statuts</option>
                        <option value="true">Actif</option>
                        <option value="false">Inactif</option>
                    </select>
                </div>
            </div>

            @if (_isLoading)
            {
                <div class="d-flex justify-content-center my-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (_products.Count == 0)
            {
                <div class="alert alert-info">
                    Aucun produit trouvé. Veuillez ajouter des produits ou modifier vos critères de recherche.
                </div>
            }
            else
            {
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Image</th>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Prix</th>
                            <th>Stock</th>
                            <th>Statut</th>
                            <th style="width: 150px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in _products)
                        {
                            <tr>
                                <td>
                                    <img src="@GetProductImageSrc(product)" alt="@product.Name" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;" />
                                </td>
                                <td>@product.Name</td>
                                <td>@product.Category?.Name</td>
                                <td>@product.Price @product.Currency</td>
                                <td>@product.StockQuantity</td>
                                <td>
                                    <span class="badge @(product.IsActive ? "bg-success" : "bg-danger")">
                                        @(product.IsActive ? "Actif" : "Inactif")
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-primary me-1" @onclick="async () => await OpenEditProductModal(product)" title="Modifier">
                                            <i class="bi bi-pencil-square"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" @onclick="async () => await DeleteProduct(product)" title="Supprimer">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>

                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                            <a class="page-link" href="javascript:void(0)" @onclick="() => ChangePage(1)">Précédent</a>
                        </li>
                        @for (int i = 1; i <= TotalPages; i++)
                        {
                            var pageNumber = i;
                            <li class="page-item @(pageNumber == _currentPage ? "active" : "")">
                                <a class="page-link" href="javascript:void(0)" @onclick="() => ChangePage(pageNumber)">@pageNumber</a>
                            </li>
                        }
                        <li class="page-item @(_currentPage == TotalPages ? "disabled" : "")">
                            <a class="page-link" href="javascript:void(0)" @onclick="() => ChangePage(TotalPages)">Suivant</a>
                        </li>
                    </ul>
                </nav>
            }
        </div>
    </div>
</div>

<!-- Formulaire de produit -->
<div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalLabel">@(_isNewProduct ? "Ajouter un produit" : "Modifier le produit")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="@_currentProduct" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <ul class="nav nav-tabs" id="productTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Général</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="images-tab" data-bs-toggle="tab" data-bs-target="#images" type="button" role="tab" aria-controls="images" aria-selected="false">Images</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="variants-tab" data-bs-toggle="tab" data-bs-target="#variants" type="button" role="tab" aria-controls="variants" aria-selected="false">Variantes</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="productTabContent">
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row p-3">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productName" class="form-label">Nom du produit</label>
                                        <InputText id="productName" class="form-control" @bind-Value="_currentProduct.Name" />
                                        <ValidationMessage For="@(() => _currentProduct.Name)" />
                                    </div>
                                    <div class="mb-3">
                                        <label for="productDescription" class="form-label">Description</label>
                                        <InputTextArea id="productDescription" class="form-control" rows="5" @bind-Value="_currentProduct.Description" />
                                        <ValidationMessage For="@(() => _currentProduct.Description)" />
                                    </div>
                                    <div class="mb-3">
                                        <label for="productCategory" class="form-label">Catégorie</label>
                                        <InputSelect id="productCategory" class="form-select" @bind-Value="_currentProduct.CategoryId">
                                            <option value="">-- Sélectionner une catégorie --</option>
                                            @foreach (var category in _categories)
                                            {
                                                <option value="@category.Id">@category.Name</option>
                                            }
                                        </InputSelect>
                                        <ValidationMessage For="@(() => _currentProduct.CategoryId)" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productPrice" class="form-label">Prix</label>
                                        <div class="input-group">
                                            <InputNumber id="productPrice" class="form-control" @bind-Value="_currentProduct.Price" />
                                            <span class="input-group-text">€</span>
                                        </div>
                                        <ValidationMessage For="@(() => _currentProduct.Price)" />
                                    </div>
                                    <div class="mb-3">
                                        <label for="productStock" class="form-label">Stock</label>
                                        <InputNumber id="productStock" class="form-control" @bind-Value="_currentProduct.StockQuantity" />
                                        <ValidationMessage For="@(() => _currentProduct.StockQuantity)" />
                                    </div>
                                    <div class="mb-3">
                                        <label for="productBrand" class="form-label">Marque</label>
                                        <InputText id="productBrand" class="form-control" @bind-Value="_currentProduct.Brand" />
                                    </div>
                                    <div class="mb-3 form-check">
                                        <InputCheckbox id="productIsActive" class="form-check-input" @bind-Value="_currentProduct.IsActive" />
                                        <label class="form-check-label" for="productIsActive">Produit actif</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="images" role="tabpanel" aria-labelledby="images-tab">
                            <div class="p-3">
                                <div class="d-flex flex-wrap gap-2 mb-2">
                                    @if (_currentProduct.Images != null && _currentProduct.Images.Any())
                                    {
                                        @foreach (var image in _currentProduct.Images)
                                        {
                                            <div class="position-relative" style="width: 100px; height: 100px;">
                                                <img src="@ProductService.GetImageUrl(image, true)" alt="Image du produit" class="img-thumbnail @(image.Id == 0 ? "uploading-image" : "")" style="width: 100px; height: 100px; object-fit: cover;" />
                                                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0" @onclick="() => RemoveImage(image.Id)">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                                @if (!image.IsMain)
                                                {
                                                    <button type="button" class="btn btn-sm btn-primary position-absolute bottom-0 start-0" @onclick="() => SetMainImage(image.Id)">
                                                        <i class="bi bi-star"></i>
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success position-absolute bottom-0 start-0">
                                                        <i class="bi bi-star-fill"></i>
                                                    </span>
                                                }
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="alert alert-warning">
                                            Aucune image pour ce produit.
                                        </div>
                                    }
                                </div>
                                <InputFile OnChange="OnFileChange" multiple class="form-control" accept="image/*" disabled="@_isNewProduct" />
                                @if (_isNewProduct)
                                {
                                    <small class="form-text text-muted">Créez d'abord le produit, puis modifiez-le pour ajouter des images.</small>
                                }
                            </div>
                        </div>
                        <div class="tab-pane fade" id="variants" role="tabpanel" aria-labelledby="variants-tab">
                            <div class="p-3">
                                <h5>Variantes du produit</h5>
                                @if (_currentProduct.Variants != null && _currentProduct.Variants.Any())
                                {
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>SKU</th>
                                                <th>Prix</th>
                                                <th>Stock</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var variant in _currentProduct.Variants)
                                            {
                                                <tr>
                                                    <td>@variant.Name</td>
                                                    <td>@variant.Sku</td>
                                                    <td>@variant.Price</td>
                                                    <td>@variant.StockQuantity</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger" @onclick="() => RemoveVariant(variant.Id)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                }
                                else
                                {
                                    <p>Aucune variante pour ce produit.</p>
                                }

                                <hr />

                                <h5>Ajouter une nouvelle variante</h5>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Nom</label>
                                        <InputText class="form-control" @bind-Value="_newVariant.Name" />
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">SKU</label>
                                        <InputText class="form-control" @bind-Value="_newVariant.Sku" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Prix</label>
                                        <InputNumber class="form-control" @bind-Value="_newVariant.Price" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Stock</label>
                                        <InputNumber class="form-control" @bind-Value="_newVariant.StockQuantity" />
                                    </div>
                                    <div class="col-md-2 mb-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-success w-100" @onclick="AddVariant">Ajouter</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            @(_isNewProduct ? "Ajouter" : "Enregistrer les modifications")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>
@code {
    private List<Product> _products = new();
    private List<Category> _categories = new();
    private Product _currentProduct = new() { Name = "", Description = "", Currency = "GNF", Variants = new List<ProductVariant>() };
    private ProductVariant _newVariant = new() { Name = "", Sku = "" };
    private bool _isNewProduct = true;
    private string _searchTerm = "";
    private int _selectedCategoryId = 0;
    private bool? _activeFilter = null;
    private int _currentPage = 1;
    private int _pageSize = 10;
    private int _totalPages = 0;
    private bool _isLoading = true;
    private bool _isUploading = false;
    private const string ProductModalId = "productModal";
private const long MaxFileSize = 5 * 1024 * 1024; // 5 MB
    private int _sellerId = 1; // ID de vendeur temporaire pour le développement

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
        _categories = await ProductService.GetCategoriesAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Initialiser le modal Bootstrap via JavaScript
            await JSRuntime.InvokeVoidAsync("modalHandler.initModal", ProductModalId);
        }
    }

    public async ValueTask DisposeAsync()
    {
        // Nettoyer les ressources si nécessaire
        try
        {
            await JSRuntime.InvokeVoidAsync("modalHandler.hideModal", ProductModalId);
        }
        catch
        {
            // Ignorer les erreurs lors de la fermeture
        }
    }



    private async Task LoadProducts(int page = 1)
    {
        try
        {
            _isLoading = true;
            _currentPage = page;
            var result = await ProductService.GetProductsAsync(_sellerId, _currentPage, _pageSize);
            _products = result.Products;
            _totalPages = result.TotalPages;
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement des produits: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }


    private async Task OpenAddProductModal()
    {
        _currentProduct = new Product
        {
            Name = "",
            Description = "",
            SellerId = _sellerId, // Utiliser l'ID de vendeur temporaire
            IsActive = true,
            Currency = "GNF",
            Images = new List<ProductImage>()
        };
        _isNewProduct = true;

        // Ouvrir le modal via JavaScript
        await JSRuntime.InvokeVoidAsync("modalHandler.showModal", ProductModalId);
    }

    private async Task OpenEditProductModal(NafaPlace.Catalog.Domain.Models.Product product)
    {
        try 
        {
            // Récupérer le produit complet depuis l'API pour s'assurer d'avoir toutes les données à jour
            var freshProduct = await ProductService.GetProductByIdAsync(product.Id);
            if (freshProduct != null)
            {
                _currentProduct = freshProduct;
            }
            else
            {
                _currentProduct = new Product
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    Price = product.Price,
                    CategoryId = product.CategoryId,
                    StockQuantity = product.StockQuantity,
                    Currency = product.Currency,
                    Brand = product.Brand,
                    Model = product.Model,
                    Weight = product.Weight,
                    Dimensions = product.Dimensions,
                    Rating = product.Rating,
                    IsActive = product.IsActive,
                    SellerId = product.SellerId,
                    Images = product.Images?.ToList() ?? new List<ProductImage>(),
                    Variants = product.Variants
                };
            }
            
            // S'assurer que la liste d'images est initialisée
            if (_currentProduct.Images == null)
            {
                _currentProduct.Images = new List<ProductImage>();
            }
            
            _isNewProduct = false;
            
            // Ouvrir le modal via JavaScript
            await JSRuntime.InvokeVoidAsync("modalHandler.showModal", ProductModalId);
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'ouverture du produit: {ex.Message}");
        }
    }

    private async Task CloseModal()
    {
        // Fermer le modal via JavaScript
        await JSRuntime.InvokeVoidAsync("modalHandler.hideModal", ProductModalId);
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            bool success = false;

            if (_isNewProduct)
            {
                // S'assurer que le SellerId est défini
                if (_currentProduct.SellerId == 0 || _currentProduct.SellerId == null)
                {
                    _currentProduct.SellerId = _sellerId;
                }

                Console.WriteLine($"🔍 DEBUG: Création produit avec SellerId = {_currentProduct.SellerId}");

                var createdProduct = await ProductService.CreateProductAsync(_currentProduct);
                if (createdProduct != null)
                {
                    success = true;
                    _currentProduct = createdProduct; // Mettre à jour le produit actuel avec la version créée
                    NotificationService.Success("Produit ajouté avec succès!");
                }
            }
            else
            {
                // 1. Extraire les nouvelles images (celles qui n'ont pas d'ID et sont en base64)
                var newImages = _currentProduct.Images.Where(i => i.Id == 0 && i.ImageUrl.StartsWith("data:image")).ToList();

                // 2. Préparer l'objet pour la mise à jour, sans les nouvelles images
                var productToUpdate = new Product
                {
                    Id = _currentProduct.Id,
                    Name = _currentProduct.Name,
                    Description = _currentProduct.Description,
                    Price = _currentProduct.Price,
                    CategoryId = _currentProduct.CategoryId,
                    StockQuantity = _currentProduct.StockQuantity,
                    Currency = _currentProduct.Currency,
                    Brand = _currentProduct.Brand,
                    Model = _currentProduct.Model,
                    Weight = _currentProduct.Weight,
                    Dimensions = _currentProduct.Dimensions,
                    Rating = _currentProduct.Rating,
                    IsActive = _currentProduct.IsActive,
                    SellerId = _currentProduct.SellerId,
                    Images = _currentProduct.Images.Where(i => i.Id != 0).ToList(), // Uniquement les images existantes
                    Variants = _currentProduct.Variants
                };

                // 3. Mettre à jour les informations de base du produit
                var updatedProduct = await ProductService.UpdateProductAsync(productToUpdate);

                if (updatedProduct != null)
                {
                    // 4. Si la mise à jour réussit et qu'il y a de nouvelles images, les télécharger
                    if (newImages.Any())
                    {
                        var imageUploadSuccess = await ProductService.UploadImagesAsync(updatedProduct.Id, newImages);
                        if (!imageUploadSuccess)
                        {
                             NotificationService.Error("Échec du téléchargement de certaines images.");
                        }
                    }
                    success = true;
                    NotificationService.Success("Produit mis à jour avec succès!");
                }
            }

            if (success)
            {
                await LoadProducts(_currentPage);
                await CloseModal();
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'enregistrement du produit: {ex.Message}");
        }
    }

    private async Task DeleteProduct(NafaPlace.Catalog.Domain.Models.Product product)
    {
        try
        {
            var result = await ProductService.DeleteProductAsync(product.Id);
            if (result)
            {
                NotificationService.Success($"Le produit '{product.Name}' a été supprimé avec succès.");
                await LoadProducts(_currentPage);
            }
            else
            {
                NotificationService.Error("Impossible de supprimer le produit.");
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de la suppression: {ex.Message}");
        }
    }

    private async Task OnFileChange(InputFileChangeEventArgs e)
    {
        try
        {
            _isUploading = true;
            var files = e.GetMultipleFiles();
            
            foreach (var file in files)
            {
                if (file != null)
                {
                    if (file.Size > MaxFileSize)
                    {
                        NotificationService.Error($"L'image '{file.Name}' dépasse la taille maximale autorisée de {MaxFileSize / (1024 * 1024)} MB.");
                        continue; // Passe au fichier suivant
                    }

                    var buffer = new byte[file.Size];
                    await file.OpenReadStream(MaxFileSize).ReadAsync(buffer);
                    var base64Image = $"data:{file.ContentType};base64,{Convert.ToBase64String(buffer)}";

                    if (_currentProduct.Images == null)
                    {
                        _currentProduct.Images = new List<ProductImage>();
                    }

                    _currentProduct.Images.Add(new ProductImage { ImageUrl = base64Image, IsMain = !_currentProduct.Images.Any(), Id = 0, ProductId = _currentProduct.Id });
                }
            }
            
            NotificationService.Success($"{files.Count} image(s) ajoutée(s) temporairement.");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement des images: {ex.Message}");
        }
        finally
        {
            _isUploading = false;
            StateHasChanged();
        }
    }

    private async Task RemoveImage(int imageId)
    {
        try
        {
            if (_currentProduct.Images == null || !_currentProduct.Images.Any())
            {
                Console.WriteLine("Aucune image à supprimer");
                return;
            }
            
            Console.WriteLine($"Suppression de l'image: ID {imageId}");
            
            var imageToRemove = _currentProduct.Images.FirstOrDefault(i => i.Id == imageId);
            if (imageToRemove != null)
            {
                bool wasMainImage = imageToRemove.IsMain;
                bool success = false;
                
                // Vérifier si c'est une image temporaire (ID négatif) ou permanente
                if (imageId < 0)
                {
                    // Pour les images temporaires, il suffit de les supprimer de la liste
                    success = true;
                    Console.WriteLine("Suppression d'une image temporaire");
                }
                else
                {
                    // Pour les images permanentes, appeler l'API pour les supprimer
                    success = await ImageService.DeleteProductImageAsync(imageToRemove.ImageUrl, _currentProduct.Id, imageId);
                }
                
                if (success)
                {
                    _currentProduct.Images.Remove(imageToRemove);
                    
                    // Si c'était l'image principale et qu'il reste des images, définir la première comme principale
                    if (wasMainImage && _currentProduct.Images.Any())
                    {
                        var firstImage = _currentProduct.Images.First();
                        firstImage.IsMain = true;
                        
                        // Appeler l'API pour définir l'image principale uniquement si ce n'est pas une image temporaire
                        if (firstImage.Id > 0 && _currentProduct.Id > 0)
                        {
                            await ImageService.SetMainImageAsync(_currentProduct.Id, firstImage.Id);
                        }
                    }
                    
                    Console.WriteLine($"Image supprimée avec succès. Images restantes: {_currentProduct.Images.Count}");
                    NotificationService.Success("Image supprimée avec succès!");
                }
                else
                {
                    NotificationService.Error("Échec de la suppression de l'image");
                }
            }
            else
            {
                Console.WriteLine($"Image non trouvée: ID {imageId}");
                NotificationService.Warning("Image non trouvée!");
            }
            
            StateHasChanged(); // Forcer la mise à jour de l'interface
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de l'image: {ex.Message}");
            NotificationService.Error($"Erreur lors de la suppression de l'image: {ex.Message}");
        }
    }

    private async Task SetMainImage(int imageId)
    {
        try
        {
            if (_currentProduct.Images == null || !_currentProduct.Images.Any())
            {
                Console.WriteLine("Aucune image à définir comme principale");
                return;
            }
            
            Console.WriteLine($"Définition de l'image principale: {imageId}");
            
            var imageToSetAsMain = _currentProduct.Images.FirstOrDefault(i => i.Id == imageId);
            if (imageToSetAsMain != null)
            {
                bool success = false;
                
                // Vérifier si c'est une image temporaire (ID négatif) ou un nouveau produit
                if (imageId < 0 || _isNewProduct)
                {
                    // Pour les images temporaires ou les nouveaux produits, on gère localement
                    success = true;
                    Console.WriteLine("Définition d'une image temporaire comme principale");
                }
                else
                {
                    // Pour les images permanentes, appeler l'API
                    success = await ImageService.SetMainImageAsync(_currentProduct.Id, imageToSetAsMain.Id);
                }
                
                if (success)
                {
                    // Mettre à jour les propriétés IsMain pour toutes les images
                    foreach (var img in _currentProduct.Images)
                    {
                        img.IsMain = (img.Id == imageId);
                    }
                    
                    // Réorganiser la liste pour que l'image principale soit en premier
                    _currentProduct.Images = new List<ProductImage>(
                        _currentProduct.Images.OrderByDescending(i => i.IsMain)
                    );
                    
                    Console.WriteLine("Image principale définie avec succès!");
                    NotificationService.Success("Image principale définie avec succès!");
                }
                else
                {
                    NotificationService.Error("Échec de la définition de l'image principale");
                }
            }
            else
            {
                Console.WriteLine($"Image non trouvée: ID {imageId}");
                NotificationService.Warning("Image non trouvée!");
            }
            
            StateHasChanged(); // Forcer la mise à jour de l'interface
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la définition de l'image principale: {ex.Message}");
            NotificationService.Error($"Erreur lors de la définition de l'image principale: {ex.Message}");
        }
    }

    private string GetProductImageSrc(NafaPlace.Catalog.Domain.Models.Product product)
    {
        try
        {
            // Vérifier si le produit a des images
            if (product?.Images != null && product.Images.Any())
            {
                // Essayer d'abord de récupérer l'image principale
                var mainImage = product.Images.FirstOrDefault(i => i.IsMain);
                if (mainImage != null)
                {
                    return ProductService.GetImageUrl(mainImage, true);
                }
                
                // Si pas d'image principale, prendre la première image disponible
                var firstImage = product.Images.FirstOrDefault();
                if (firstImage != null)
                {
                    return ProductService.GetImageUrl(firstImage, true);
                }
            }
            
            // Si aucune image n'est disponible, retourner l'image par défaut
            return GetDefaultImageUrl();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de l'image du produit: {ex.Message}");
            return GetDefaultImageUrl();
        }
    }

    private string GetDefaultImageUrl()
    {
        // Image par défaut simplifiée (petit carré gris)
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
    }

    private async Task OnSearch()
    {
        await LoadProducts(1);
    }

    private async Task OnCategoryFilterChange(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int categoryId))
        {
            _selectedCategoryId = categoryId;
        }
        await LoadProducts(1);
    }

    private async Task OnActiveFilterChange(ChangeEventArgs e)
    {
        var value = e.Value?.ToString();
        _activeFilter = string.IsNullOrEmpty(value) ? null : value == "true";
        await LoadProducts(1);
    }

    private async Task ChangePage(int page)
    {
        await LoadProducts(page);
    }

    private int TotalPages => _totalPages;

    private void AddVariant()
    {
        if (!string.IsNullOrWhiteSpace(_newVariant.Name) && !string.IsNullOrWhiteSpace(_newVariant.Sku))
        {
            _currentProduct.Variants.Add(_newVariant);
            _newVariant = new() { Name = "", Sku = "" };
        }
    }

    private void RemoveVariant(int variantId)
    {
        var variant = _currentProduct.Variants.FirstOrDefault(v => v.Id == variantId);
        if (variant != null)
        {
            _currentProduct.Variants.Remove(variant);
        }
    }
}
